from flask import Flask, request
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from controllers.log_controller import get_logs, clear_logs, download_logs

# Try to import ML controllers, fallback to simple responses if they fail
try:
    from controllers.find_similar_controller import find_similar
    from controllers.sketch_controller import sketch
    from controllers.text_generation_controller import generate_text_controller
    ML_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"ML features not available due to import error: {e}")
    ML_FEATURES_AVAILABLE = False

    # Define fallback functions
    def find_similar():
        from views.responses import success_response
        from utils.logger import logger
        logger.info("Find similar endpoint called (ML features disabled)")
        return success_response({"message": "Find similar feature temporarily disabled due to dependency issues"})

    def sketch():
        from views.responses import success_response
        from utils.logger import logger
        logger.info("Sketch endpoint called (ML features disabled)")
        return success_response({"message": "Sketch feature temporarily disabled due to dependency issues"})

    def generate_text_controller():
        from views.responses import success_response
        from utils.logger import logger
        logger.info("Generate text endpoint called (ML features disabled)")
        return success_response({"message": "Text generation feature temporarily disabled due to dependency issues"})
from PIL import Image, ImageEnhance, ImageFilter
import io
import base64
import os
import threading
import time
from utils.logger import logger
from views.responses import success_response, error_response

app = Flask(__name__)
CORS(app, origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")

if ML_FEATURES_AVAILABLE:
    logger.info("Starting Sketch-Based Image Retrieval API (Full Mode)")
else:
    logger.info("Starting Sketch-Based Image Retrieval API (Debugger Mode - ML features disabled)")

@app.route('/', methods=['GET'])
def home():
    return {"message": "Welcome to the Sketch-Based Image Retrieval API"}, 200

@app.route('/find_similar', methods=['POST'])
def find_similar_endpoint():
    return find_similar()

@app.route('/sketch', methods=['POST'])
def sketch_endpoint():
    return sketch()

@app.route('/generate_text', methods=['POST'])
def generate_text_endpoint():
    return generate_text_controller()

# Log management endpoints
@app.route('/logs', methods=['GET'])
def logs_endpoint():
    return get_logs()

@app.route('/logs/clear', methods=['POST'])
def clear_logs_endpoint():
    return clear_logs()

@app.route('/logs/download', methods=['GET'])
def download_logs_endpoint():
    return download_logs()

# WebSocket events for real-time log streaming
@socketio.on('connect')
def handle_connect():
    logger.info(f"Client connected for log streaming")
    emit('status', {'msg': 'Connected to log stream'})

@socketio.on('disconnect')
def handle_disconnect():
    logger.info('Client disconnected from log streaming')

@socketio.on('join_logs')
def handle_join_logs():
    logger.info('Client joined log streaming room')
    emit('status', {'msg': 'Joined log streaming'})

# Log file monitoring for real-time updates
LOG_FILE_PATH = 'logs/sbir_api.log'
last_log_size = 0

def monitor_log_file():
    """Monitor log file for changes and emit new logs via WebSocket"""
    global last_log_size

    while True:
        try:
            if os.path.exists(LOG_FILE_PATH):
                current_size = os.path.getsize(LOG_FILE_PATH)

                if current_size > last_log_size:
                    # File has grown, read new content
                    with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
                        file.seek(last_log_size)
                        new_content = file.read()

                        if new_content.strip():
                            # Parse new log lines
                            new_lines = new_content.strip().split('\n')
                            for line in new_lines:
                                if line.strip():
                                    from controllers.log_controller import parse_log_line
                                    parsed_log = parse_log_line(line.strip())
                                    socketio.emit('new_log', parsed_log)

                    last_log_size = current_size
                elif current_size < last_log_size:
                    # File was truncated (cleared)
                    last_log_size = 0
                    socketio.emit('logs_cleared', {'message': 'Logs were cleared'})

            time.sleep(1)  # Check every second

        except Exception as e:
            logger.error(f"Error monitoring log file: {e}")
            time.sleep(5)  # Wait longer on error

# Start log monitoring in background thread
log_monitor_thread = threading.Thread(target=monitor_log_file, daemon=True)
log_monitor_thread.start()

if __name__ == '__main__':
    logger.info("Starting Flask server on port 5000")
    socketio.run(app, debug=True, port=5000, host='0.0.0.0')