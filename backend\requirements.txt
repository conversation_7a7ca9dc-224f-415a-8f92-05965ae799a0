absl-py==2.1.0
annotated-types==0.7.0
anyio==4.8.0
astunparse==1.6.3
blinker==1.9.0
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
distro==1.9.0
Flask==3.1.0
flask-cors==5.0.1
flatbuffers==25.2.10
gast==0.6.0
google-pasta==0.2.0
groq==0.18.0
grpcio==1.70.0
h11==0.14.0
h5py==3.13.0
httpcore==1.0.7
httpx==0.28.1
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.5
joblib==1.4.2
keras==3.8.0
libclang==18.1.1
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
ml-dtypes==0.4.1
namex==0.0.8
numpy==1.26.4
opencv-python==*********
opt_einsum==3.4.0
optree==0.14.1
packaging==24.2
pillow==11.1.0
protobuf==3.20.3
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
python-dotenv==1.0.1
requests==2.32.3
rich==13.9.4
scikit-learn==1.6.1
scipy==1.13.1
setuptools==75.8.2
six==1.17.0
sniffio==1.3.1
tensorboard==2.18.0
tensorboard-data-server==0.7.2
tensorflow==2.18.0
# tensorflow-intel is Windows-specific and not needed in Docker
termcolor==2.5.0
threadpoolctl==3.5.0
typing_extensions==4.12.2
urllib3==2.3.0
Werkzeug==3.1.3
wheel==0.45.1
wrapt==1.17.2
gunicorn==23.0.0
flask-socketio==5.3.6
python-socketio==5.11.0