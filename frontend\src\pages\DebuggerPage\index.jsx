import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { io } from 'socket.io-client';
import { logService } from '../../services/api';
import './index.css';

const DebuggerPage = () => {
  const [logs, setLogs] = useState([]);
  const [filteredLogs, setFilteredLogs] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [maxLines, setMaxLines] = useState(100);
  const [autoScroll, setAutoScroll] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const socketRef = useRef(null);
  const logsEndRef = useRef(null);
  const logContainerRef = useRef(null);

  // Initialize WebSocket connection
  useEffect(() => {
    // Use the same host as the current page, but port 5000 for the backend
    const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
      ? 'http://127.0.0.1:5000'
      : `http://${window.location.hostname}:5000`;

    console.log('Connecting to WebSocket at:', API_BASE_URL);
    socketRef.current = io(API_BASE_URL);

    socketRef.current.on('connect', () => {
      console.log('WebSocket connected successfully');
      setIsConnected(true);
      setError('');
      socketRef.current.emit('join_logs');
    });

    socketRef.current.on('disconnect', () => {
      console.log('WebSocket disconnected');
      setIsConnected(false);
    });

    socketRef.current.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setError('Failed to connect to log server: ' + error.message);
      setIsConnected(false);
    });

    socketRef.current.on('new_log', (logEntry) => {
      setLogs(prevLogs => {
        const newLogs = [...prevLogs, logEntry];
        // Keep only the last maxLines entries
        return newLogs.slice(-maxLines);
      });
    });

    socketRef.current.on('logs_cleared', () => {
      setLogs([]);
      setFilteredLogs([]);
    });

    socketRef.current.on('status', (data) => {
      console.log('WebSocket status:', data.msg);
    });

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [maxLines]);

  // Load initial logs
  useEffect(() => {
    // Test basic connectivity first
    const testConnectivity = async () => {
      try {
        const response = await fetch('http://127.0.0.1:5000/');
        const data = await response.json();
        console.log('Backend connectivity test successful:', data);
      } catch (error) {
        console.error('Backend connectivity test failed:', error);
        setError('Cannot connect to backend server. Please ensure the backend is running on port 5000.');
      }
    };

    testConnectivity();
    loadLogs();
  }, []);

  // Filter logs based on search term and level
  useEffect(() => {
    console.log('Filtering logs. Total logs:', logs.length, 'Search term:', searchTerm, 'Selected level:', selectedLevel);
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.file.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.logger.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedLevel) {
      filtered = filtered.filter(log => log.level === selectedLevel);
    }

    console.log('Filtered logs:', filtered.length, 'entries');
    setFilteredLogs(filtered);
  }, [logs, searchTerm, selectedLevel]);

  // Auto scroll to bottom
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [filteredLogs, autoScroll]);

  const loadLogs = async () => {
    setIsLoading(true);
    setError('');
    try {
      console.log('Loading logs with maxLines:', maxLines);
      const response = await logService.getLogs({ lines: maxLines });
      console.log('Logs response:', response.data);
      const logsData = response.data.logs || [];
      console.log('Setting logs:', logsData.length, 'entries');
      setLogs(logsData);
    } catch (err) {
      console.error('Error loading logs:', err);
      setError('Failed to load logs: ' + (err.response?.data?.message || err.message));
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = async () => {
    if (!window.confirm('Are you sure you want to clear all logs?')) {
      return;
    }

    try {
      await logService.clearLogs();
      setLogs([]);
      setFilteredLogs([]);
    } catch (err) {
      setError('Failed to clear logs: ' + (err.response?.data?.message || err.message));
    }
  };

  const downloadLogs = async () => {
    try {
      const response = await logService.downloadLogs();
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'sbir_api.log');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to download logs: ' + (err.response?.data?.message || err.message));
    }
  };

  const getLogLevelClass = (level) => {
    switch (level?.toUpperCase()) {
      case 'ERROR': return 'log-error';
      case 'WARNING': return 'log-warning';
      case 'INFO': return 'log-info';
      case 'DEBUG': return 'log-debug';
      default: return 'log-default';
    }
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    try {
      // Parse the timestamp format: 2024-04-30 11:59:23,456
      const [datePart, timePart] = timestamp.split(' ');
      const [time, ms] = timePart.split(',');
      return `${time}.${ms}`;
    } catch {
      return timestamp;
    }
  };

  return (
    <div className="debugger-page">
      <motion.div
        className="debugger-container"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="debugger-header">
          <h1>Live Log Debugger</h1>
          <div className="connection-status">
            <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
              {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
            </span>
          </div>
        </div>

        <div className="debugger-controls">
          <div className="control-group">
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />

            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="level-filter"
            >
              <option value="">All Levels</option>
              <option value="DEBUG">DEBUG</option>
              <option value="INFO">INFO</option>
              <option value="WARNING">WARNING</option>
              <option value="ERROR">ERROR</option>
            </select>

            <input
              type="number"
              placeholder="Max lines"
              value={maxLines}
              onChange={(e) => setMaxLines(parseInt(e.target.value) || 100)}
              className="max-lines-input"
              min="10"
              max="1000"
            />
          </div>

          <div className="control-group">
            <label className="auto-scroll-label">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
              />
              Auto Scroll
            </label>

            <button onClick={loadLogs} disabled={isLoading} className="btn btn-primary">
              {isLoading ? 'Loading...' : 'Refresh'}
            </button>

            <button onClick={clearLogs} className="btn btn-warning">
              Clear Logs
            </button>

            <button onClick={downloadLogs} className="btn btn-secondary">
              Download
            </button>
          </div>
        </div>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <div className="logs-info">
          <span>Showing {filteredLogs.length} of {logs.length} log entries</span>
        </div>

        <div className="logs-container" ref={logContainerRef}>
          <div className="logs-content">
            {filteredLogs.map((log, index) => (
              <div key={index} className={`log-entry ${getLogLevelClass(log.level)}`}>
                <span className="log-timestamp">{formatTimestamp(log.timestamp)}</span>
                <span className="log-level">{log.level}</span>
                <span className="log-file">{log.file}</span>
                <span className="log-message">{log.message}</span>
              </div>
            ))}
            <div ref={logsEndRef} />
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default DebuggerPage;
