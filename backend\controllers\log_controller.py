import os
import json
from flask import request
from flask_socketio import emit
from views.responses import success_response, error_response
from utils.logger import logger

LOG_FILE_PATH = 'logs/sbir_api.log'

def get_logs():
    """Get recent logs from the log file"""
    try:
        lines_count = request.args.get('lines', 100, type=int)
        search_term = request.args.get('search', '', type=str)
        log_level = request.args.get('level', '', type=str)
        
        if not os.path.exists(LOG_FILE_PATH):
            return success_response({"logs": [], "message": "Log file not found"})
        
        with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        # Get the last N lines
        recent_lines = lines[-lines_count:] if lines_count > 0 else lines
        
        # Parse and filter logs
        parsed_logs = []
        for line in recent_lines:
            line = line.strip()
            if not line:
                continue
                
            # Parse log line
            log_entry = parse_log_line(line)
            
            # Apply filters
            if search_term and search_term.lower() not in line.lower():
                continue
                
            if log_level and log_level.upper() not in line.upper():
                continue
                
            parsed_logs.append(log_entry)
        
        return success_response({
            "logs": parsed_logs,
            "total_lines": len(lines),
            "filtered_lines": len(parsed_logs)
        })
        
    except Exception as e:
        logger.error(f"Error reading logs: {e}", exc_info=True)
        return error_response(str(e), 500)

def parse_log_line(line):
    """Parse a log line into structured data"""
    try:
        # Expected format: 2024-04-30 11:59:23,456 - API - INFO - app.py:15 - Starting Flask server
        parts = line.split(' - ', 4)
        
        if len(parts) >= 4:
            timestamp = parts[0]
            logger_name = parts[1]
            level = parts[2]
            
            # Split the remaining part to get file info and message
            remaining = parts[3]
            if len(parts) == 5:
                file_info = remaining
                message = parts[4]
            else:
                # If no file info, treat as message
                file_info = ""
                message = remaining
            
            return {
                "timestamp": timestamp,
                "logger": logger_name,
                "level": level,
                "file": file_info,
                "message": message,
                "raw": line
            }
        else:
            # If parsing fails, return raw line
            return {
                "timestamp": "",
                "logger": "",
                "level": "UNKNOWN",
                "file": "",
                "message": line,
                "raw": line
            }
    except Exception:
        return {
            "timestamp": "",
            "logger": "",
            "level": "UNKNOWN", 
            "file": "",
            "message": line,
            "raw": line
        }

def clear_logs():
    """Clear the log file"""
    try:
        if os.path.exists(LOG_FILE_PATH):
            with open(LOG_FILE_PATH, 'w') as file:
                file.write("")
            logger.info("Log file cleared by user request")
            return success_response({"message": "Logs cleared successfully"})
        else:
            return error_response("Log file not found", 404)
    except Exception as e:
        logger.error(f"Error clearing logs: {e}", exc_info=True)
        return error_response(str(e), 500)

def download_logs():
    """Download the complete log file"""
    try:
        if not os.path.exists(LOG_FILE_PATH):
            return error_response("Log file not found", 404)
            
        from flask import send_file
        return send_file(LOG_FILE_PATH, as_attachment=True, download_name='sbir_api.log')
        
    except Exception as e:
        logger.error(f"Error downloading logs: {e}", exc_info=True)
        return error_response(str(e), 500)
