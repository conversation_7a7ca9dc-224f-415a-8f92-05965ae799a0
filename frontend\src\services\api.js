import axios from 'axios';

const API_BASE_URL = 'http://127.0.0.1:5000';

const api = axios.create({
  baseURL: API_BASE_URL,
});

export const imageService = {
  findSimilar: async (imageData) => {
    const formData = new FormData();
    formData.append('image', imageData);
    return api.post('/find_similar', formData);
  },

  generateSketch: async (imageData) => {
    const formData = new FormData();
    formData.append('image', imageData);
    return api.post('/sketch', formData);
  },

  generateText: async (imageData) => {
    const formData = new FormData();
    formData.append('image', imageData);
    return api.post('/generate_text', formData);
  }
};

export const logService = {
  getLogs: async (params = {}) => {
    return api.get('/logs', { params });
  },

  clearLogs: async () => {
    return api.post('/logs/clear');
  },

  downloadLogs: async () => {
    return api.get('/logs/download', { responseType: 'blob' });
  }
};